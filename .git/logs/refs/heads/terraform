0000000000000000000000000000000000000000 0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1753781801 +0200	branch: Created from HEAD
0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 00da7ba802ea32c6144ba8ad70bd291a4755dbbe <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1753786950 +0200	commit: Initial Terraform setup.
00da7ba802ea32c6144ba8ad70bd291a4755dbbe 7d96b698df4972d83ded3e273c37544041034d8c <PERSON><PERSON> <marcin.marus<PERSON><EMAIL>> 1753797552 +0200	commit (amend): Initial Terraform setup.
7d96b698df4972d83ded3e273c37544041034d8c 82eee91bc2492ddeb6c76665abada9bd56f0c2b0 <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1753799124 +0200	commit (amend): Initial Terraform setup.
82eee91bc2492ddeb6c76665abada9bd56f0c2b0 d9a4b1b70cd80bd8ef533853718984b2d0730c63 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initial Terraform setup.
d9a4b1b70cd80bd8ef533853718984b2d0730c63 eda39005580d8c2e2a74c6cd3993e4fc2017a85b Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initial Terraform setup.
eda39005580d8c2e2a74c6cd3993e4fc2017a85b de8c7a46e9d48ec8e5470ca558a50ee0b261d844 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Add GCP provider.
de8c7a46e9d48ec8e5470ca558a50ee0b261d844 2b35a945ff6f6a9de9875dd6f3c1c4f77abb75c4 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Create Docker artifact registry for PHP and Apache containers.
2b35a945ff6f6a9de9875dd6f3c1c4f77abb75c4 bfe6ce3fb2945fb53162b8583aa3d9b88f528fb8 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Create Docker artifact registry for PHP and Apache images.
bfe6ce3fb2945fb53162b8583aa3d9b88f528fb8 69ff40f8a1a5ad4cfe442b8aeef728563cb42655 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Create Docker artifact registry for PHP and Apache images.
69ff40f8a1a5ad4cfe442b8aeef728563cb42655 2f6d03ce15b8f53dca192af379df980fe0b18522 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Initialize GitLab pipelines.
2f6d03ce15b8f53dca192af379df980fe0b18522 94f5a58ba20bf606d38566da0e829ba863af95f2 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initialize GitLab pipelines.
94f5a58ba20bf606d38566da0e829ba863af95f2 0cc1b924b983878c8c01a8273d5d96354d995532 Marcin Maruszewski <<EMAIL>> 1753818571 +0200	commit (amend): Initialize GitLab pipelines.
