version: '3'

dotenv:
  - env/.common.env

vars:
  DOCKER_COMPOSE: docker compose --env-file ./env/.common.env

tasks:
  default:
    deps: [help]

  help:
    desc: Print commands help
    silent: true
    cmds:
      - task --list

  init:
    desc: Initialize local environment
    cmds:
      - cmd: for f in env/.local.*.env; do cp "$f" "env/${f#env/.local}"; done
        silent: true
      - cmd: task build
        silent: true
      - cmd: task up
        silent: true
      - cmd: '{{.DOCKER_COMPOSE}} exec grumphp sh -c "cd drupal && composer install"'

  build:
    desc: Build Docker images
    cmd: '{{.DOCKER_COMPOSE}} build {{.CLI_ARGS}}'

  up:
    desc: Startup containers
    run: when_changed
    status:
      - '{{.DOCKER_COMPOSE}} up -d --dry-run 2>&1 | grep Started | wc -l | xargs test 0 -eq'
    cmds:
      - cmd: '{{.DOCKER_COMPOSE}} up -d --remove-orphans'
        silent: true
      - cmd: |
          echo -n "Waiting for database to be ready"
          until {{.DOCKER_COMPOSE}} exec mysql mysql -u {{.MYSQL_USER}} -p{{.MYSQL_PASSWORD}} {{.MYSQL_DATABASE}} -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{{.MYSQL_DATABASE}}';" > /dev/null 2>&1; do
            echo -n "."
            sleep 5
          done
          echo ""
          echo "Database is ready!"
        silent: true

  stop:
    desc: Stop containers
    status:
      - '{{.DOCKER_COMPOSE}} ps -q --status=running | grep . | wc -l | xargs test 0 -eq'
    cmds:
      - '{{.DOCKER_COMPOSE}} stop'

  restart:
    desc: 'Restart application (using docker compose stop + up instead of docker compose restart).'
    method: none
    cmds:
      - task: stop
        silent: true
      - task: up
        silent: true

  prune:
    desc: Remove containers and their volumes
    cmds:
      - echo "Removing containers..."
      - '{{.DOCKER_COMPOSE}} rm -svf'

  yolo:
    desc: Recreate environment
    cmds:
      - task: prune
        silent: true
      - task: init
        silent: true

  shell:
    desc: |
      Access container via shell (php by default)
      Examples:
        task shell            # Access php container
        task shell -- mysql   # Access mysql container
        task shell -- apache  # Access apache container
    cmds:
      - '{{ .DOCKER_COMPOSE }} exec {{if .CLI_ARGS}}{{.CLI_ARGS}}{{else}}php{{end}} bash'

  terraform:
    desc: |
      Run terraform commands
      Examples:
        task terraform -- fmt
        task terraform
    cmd: '{{ .DOCKER_COMPOSE }} run --rm terraform {{.CLI_ARGS}}'
    silent: true

  terraform:init:
    desc: Initialize terraform backend for custom backend
    dotenv:
      - env/.terraform.env
    preconditions:
      - test -f env/.terraform.env
    cmds:
      - envsubst < ./terraform/template.backend.hcl > ./terraform/backend.hcl
      - '{{ .DOCKER_COMPOSE }} run --rm terraform init -backend-config=./backend.hcl'
    silent: true
