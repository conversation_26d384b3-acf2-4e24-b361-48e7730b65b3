#!/bin/bash

# Skrypt do konfiguracji zmiennych środowiskowych GitLab CI/CD
# Użycie: ./scripts/setup-gitlab-variables.sh

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcje pomocnicze
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Sprawdź czy gitlab CLI jest zainstalowane
check_gitlab_cli() {
    if ! command -v glab &> /dev/null; then
        print_error "GitLab CLI (glab) nie jest zainstalowane"
        print_info "Zainstaluj używając: https://gitlab.com/gitlab-org/cli"
        exit 1
    fi
    print_success "GitLab CLI jest dostępne"
}

# Sprawdź czy jesteś w repozytorium GitLab
check_gitlab_repo() {
    if ! glab repo view &> /dev/null; then
        print_error "Nie jesteś w repozytorium GitLab lub nie jesteś zalogowany"
        print_info "Zaloguj się używając: glab auth login"
        exit 1
    fi
    print_success "Połączenie z GitLab potwierdzone"
}

# Funkcja do kodowania pliku w base64
encode_service_account() {
    local file_path="$1"
    
    if [ ! -f "$file_path" ]; then
        print_error "Plik service account nie istnieje: $file_path"
        return 1
    fi
    
    # Sprawdź czy plik jest poprawnym JSON
    if ! jq empty "$file_path" 2>/dev/null; then
        print_error "Plik nie jest poprawnym JSON: $file_path"
        return 1
    fi
    
    # Zakoduj w base64 (bez łamania linii)
    base64 -w 0 "$file_path"
}

# Główna funkcja konfiguracji
setup_variables() {
    print_info "Rozpoczynam konfigurację zmiennych GitLab CI/CD..."
    
    # Ścieżka do pliku service account
    local service_account_file="terraform/gcp.json"
    
    if [ ! -f "$service_account_file" ]; then
        print_warning "Plik service account nie znaleziony: $service_account_file"
        read -p "Podaj ścieżkę do pliku service account: " service_account_file
    fi
    
    print_info "Koduję service account key..."
    local encoded_key
    encoded_key=$(encode_service_account "$service_account_file")
    
    if [ $? -ne 0 ]; then
        print_error "Nie udało się zakodować service account key"
        exit 1
    fi
    
    print_success "Service account key zakodowany"
    
    # Ustaw zmienną GCP_SERVICE_ACCOUNT_KEY
    print_info "Ustawiam zmienną GCP_SERVICE_ACCOUNT_KEY..."
    if glab variable set GCP_SERVICE_ACCOUNT_KEY "$encoded_key" --masked; then
        print_success "Zmienna GCP_SERVICE_ACCOUNT_KEY została ustawiona"
    else
        print_error "Nie udało się ustawić zmiennej GCP_SERVICE_ACCOUNT_KEY"
        exit 1
    fi
    
    # Opcjonalnie ustaw inne zmienne
    print_info "Sprawdzam inne zmienne..."
    
    # GCP_PROJECT_ID
    local project_id
    project_id=$(jq -r '.project_id' "$service_account_file" 2>/dev/null || echo "")
    
    if [ -n "$project_id" ] && [ "$project_id" != "null" ]; then
        print_info "Ustawiam GCP_PROJECT_ID: $project_id"
        glab variable set GCP_PROJECT_ID "$project_id"
        print_success "Zmienna GCP_PROJECT_ID została ustawiona"
    else
        print_warning "Nie udało się odczytać project_id z service account"
        read -p "Podaj GCP Project ID (lub naciśnij Enter aby pominąć): " project_id
        if [ -n "$project_id" ]; then
            glab variable set GCP_PROJECT_ID "$project_id"
            print_success "Zmienna GCP_PROJECT_ID została ustawiona"
        fi
    fi
    
    # GCP_REGION
    print_info "Ustawiam GCP_REGION na europe-central2 (domyślnie)"
    glab variable set GCP_REGION "europe-central2"
    print_success "Zmienna GCP_REGION została ustawiona"
}

# Funkcja do wyświetlenia aktualnych zmiennych
show_variables() {
    print_info "Aktualne zmienne CI/CD:"
    glab variable list
}

# Funkcja do testowania konfiguracji
test_configuration() {
    print_info "Testowanie konfiguracji..."
    
    # Sprawdź czy zmienne są ustawione
    local variables=("GCP_SERVICE_ACCOUNT_KEY" "GCP_PROJECT_ID" "GCP_REGION")
    
    for var in "${variables[@]}"; do
        if glab variable get "$var" &> /dev/null; then
            print_success "Zmienna $var jest ustawiona"
        else
            print_warning "Zmienna $var nie jest ustawiona"
        fi
    done
}

# Menu główne
main() {
    print_info "🚀 Konfiguracja GitLab CI/CD dla Content Factory"
    echo
    
    check_gitlab_cli
    check_gitlab_repo
    
    echo
    print_info "Wybierz akcję:"
    echo "1) Skonfiguruj wszystkie zmienne"
    echo "2) Pokaż aktualne zmienne"
    echo "3) Przetestuj konfigurację"
    echo "4) Wyjście"
    echo
    
    read -p "Wybór (1-4): " choice
    
    case $choice in
        1)
            setup_variables
            ;;
        2)
            show_variables
            ;;
        3)
            test_configuration
            ;;
        4)
            print_info "Do widzenia!"
            exit 0
            ;;
        *)
            print_error "Nieprawidłowy wybór"
            exit 1
            ;;
    esac
    
    echo
    print_success "Operacja zakończona pomyślnie!"
    print_info "Możesz teraz uruchomić pipeline w GitLab"
}

# Uruchom główną funkcję
main "$@"
