resource "google_artifact_registry_repository" "drupal_php" {
  format        = "DOCKER"
  repository_id = "drupal-php"
}
resource "google_artifact_registry_repository" "drupal_apache" {
  format        = "DOCKER"
  repository_id = "drupal-apache"
}

output "drupal_php_url" {
  value = "${google_artifact_registry_repository.drupal_php.location}-docker.pkg.dev/${google_artifact_registry_repository.drupal_php.project}/${google_artifact_registry_repository.drupal_php.repository_id}"
}
output "drupal_apache_url" {
  value = "${google_artifact_registry_repository.drupal_apache.location}-docker.pkg.dev/${google_artifact_registry_repository.drupal_apache.project}/${google_artifact_registry_repository.drupal_apache.repository_id}"
}
