address        = "https://$GIT<PERSON>B_HOST/api/v4/projects/$GITLAB_PROJECT_ID/terraform/state/$GITLAB_STATE_NAME"
lock_address   = "https://$GITLAB_HOST/api/v4/projects/$GITLAB_PROJECT_ID/terraform/state/$GITLAB_STATE_NAME/lock"
unlock_address = "https://$GITLAB_HOST/api/v4/projects/$GITLAB_PROJECT_ID/terraform/state/$GITLAB_STATE_NAME/lock"
username       = "${GITLAB_USER_LOGIN}"
password       = "${GITLAB_ACCESS_TOKEN}"
lock_method    = "POST"
unlock_method  = "DELETE"
retry_wait_min = 5
