# GitLab CI/CD Pipeline for Drupal Content Factory
# Builds PHP and Apache Docker images and pushes them to Google Artifact Registry

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKERFILE_PATH: "docker/drupal.Dockerfile"

  # GCP configuration - reads from TF_VAR_gcp_project
  GCP_PROJECT_ID: "${TF_VAR_gcp_project}"
  GCP_REGION: "europe-central2"

# Default configuration
default:
  tags:
    - content-factory
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - docker info

# Pipeline stages
stages:
  - validate
  - terraform
  - build
  - push

# Validate stage - check code quality and configuration
validate:
  stage: validate
  image: alpine:latest
  script:
    - echo "🔍 Validating project configuration..."
    - |
      if [ ! -f "$DOCKERFILE_PATH" ]; then
        echo "❌ Dockerfile not found at $DOCKERFILE_PATH"
        exit 1
      fi
    - echo "✅ Dockerfile validation passed"
    - |
      if [ ! -d "drupal" ]; then
        echo "❌ Drupal directory not found"
        exit 1
      fi
    - echo "✅ Drupal directory validation passed"
    - echo "✅ All validations passed"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Get Terraform outputs for registry URLs
terraform:outputs:
  stage: terraform
  image: hashicorp/terraform:1.12
  before_script:
    - echo "🔐 Authenticating with Google Cloud..."
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcp-key.json
    - cd terraform
    # Initialize Terraform backend
    - |
      if [ -f "backend.hcl" ]; then
        terraform init -backend-config=backend.hcl
      else
        echo "⚠️ backend.hcl not found, using default backend"
        terraform init
      fi
  script:
    - echo "📋 Getting Terraform outputs..."
    - terraform output -json > ../terraform-outputs.json
    - |
      # Extract registry URLs from Terraform outputs
      DRUPAL_PHP_URL=$(terraform output -raw drupal_php_url)
      DRUPAL_APACHE_URL=$(terraform output -raw drupal_apache_url)

      echo "PHP Registry URL: $DRUPAL_PHP_URL"
      echo "Apache Registry URL: $DRUPAL_APACHE_URL"

      # Save to environment file for next stages
      echo "DRUPAL_PHP_REGISTRY=$DRUPAL_PHP_URL" > ../registry-urls.env
      echo "DRUPAL_APACHE_REGISTRY=$DRUPAL_APACHE_URL" >> ../registry-urls.env
      echo "PHP_IMAGE_NAME=$DRUPAL_PHP_URL/drupal-php" >> ../registry-urls.env
      echo "APACHE_IMAGE_NAME=$DRUPAL_APACHE_URL/drupal-apache" >> ../registry-urls.env
    - echo "✅ Registry URLs extracted from Terraform"
    - cat ../registry-urls.env
  artifacts:
    reports:
      dotenv: registry-urls.env
    paths:
      - terraform-outputs.json
      - registry-urls.env
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Build PHP image
build:php:
  stage: build
  dependencies:
    - terraform:outputs
  script:
    - echo "🐘 Building Drupal PHP image..."
    - echo "Using registry: $DRUPAL_PHP_REGISTRY"
    - echo "Image name: $PHP_IMAGE_NAME"
    - |
      docker build \
        --target app_php \
        --file $DOCKERFILE_PATH \
        --tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA \
        --tag $PHP_IMAGE_NAME:latest \
        --build-arg USE_XDEBUG=0 \
        .
    - echo "✅ PHP image built successfully"
    - docker images | grep drupal-php || docker images
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Build Apache image
build:apache:
  stage: build
  dependencies:
    - terraform:outputs
  script:
    - echo "🌐 Building Drupal Apache image..."
    - echo "Using registry: $DRUPAL_APACHE_REGISTRY"
    - echo "Image name: $APACHE_IMAGE_NAME"
    - |
      docker build \
        --target apache \
        --file $DOCKERFILE_PATH \
        --tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA \
        --tag $APACHE_IMAGE_NAME:latest \
        --build-arg USE_XDEBUG=0 \
        .
    - echo "✅ Apache image built successfully"
    - docker images | grep drupal-apache || docker images
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Push PHP image to Artifact Registry
push:php:
  stage: push
  dependencies:
    - terraform:outputs
    - build:php
  before_script:
    - echo "🔐 Authenticating with Google Cloud..."
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev
    - rm /tmp/gcp-key.json
  script:
    - echo "📦 Pushing PHP image to Artifact Registry..."
    - |
      # Rebuild image to ensure it's available
      docker build \
        --target app_php \
        --file $DOCKERFILE_PATH \
        --tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA \
        --build-arg USE_XDEBUG=0 \
        .
    - docker push $PHP_IMAGE_NAME:$CI_COMMIT_SHA
    - |
      if [ "$CI_COMMIT_BRANCH" = "master" ]; then
        docker tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA $PHP_IMAGE_NAME:latest
        docker push $PHP_IMAGE_NAME:latest
        echo "✅ PHP image pushed with 'latest' tag"
      fi
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        docker tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA $PHP_IMAGE_NAME:$CI_COMMIT_TAG
        docker push $PHP_IMAGE_NAME:$CI_COMMIT_TAG
        echo "✅ PHP image pushed with tag '$CI_COMMIT_TAG'"
      fi
    - echo "✅ PHP image pushed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Push Apache image to Artifact Registry
push:apache:
  stage: push
  dependencies:
    - terraform:outputs
    - build:apache
  before_script:
    - echo "🔐 Authenticating with Google Cloud..."
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev
    - rm /tmp/gcp-key.json
  script:
    - echo "📦 Pushing Apache image to Artifact Registry..."
    - |
      # Rebuild image to ensure it's available
      docker build \
        --target apache \
        --file $DOCKERFILE_PATH \
        --tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA \
        --build-arg USE_XDEBUG=0 \
        .
    - docker push $APACHE_IMAGE_NAME:$CI_COMMIT_SHA
    - |
      if [ "$CI_COMMIT_BRANCH" = "master" ]; then
        docker tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA $APACHE_IMAGE_NAME:latest
        docker push $APACHE_IMAGE_NAME:latest
        echo "✅ Apache image pushed with 'latest' tag"
      fi
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        docker tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA $APACHE_IMAGE_NAME:$CI_COMMIT_TAG
        docker push $APACHE_IMAGE_NAME:$CI_COMMIT_TAG
        echo "✅ Apache image pushed with tag '$CI_COMMIT_TAG'"
      fi
    - echo "✅ Apache image pushed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG