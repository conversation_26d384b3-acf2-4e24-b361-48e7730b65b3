# GitLab CI/CD Pipeline for Drupal Content Factory
# Builds PHP and Apache Docker images and pushes them to Google Artifact Registry

variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKERFILE_PATH: "docker/drupal.Dockerfile"

  # GCP configuration
  GCP_PROJECT_ID: "clz200508-dp-pl-con-fac-dev"
  GCP_REGION: "europe-central2"

  # Artifact Registry URLs (from Terraform outputs)
  DRUPAL_PHP_REGISTRY: "${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/drupal-php"
  DRUPAL_APACHE_REGISTRY: "${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/drupal-apache"

  # Image tags
  PHP_IMAGE_NAME: "${DRUPAL_PHP_REGISTRY}/drupal-php"
  APACHE_IMAGE_NAME: "${DRUPAL_APACHE_REGISTRY}/drupal-apache"

# Default configuration
default:
  tags:
    - content-factory
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - docker info

# Pipeline stages
stages:
  - validate
  - build
  - push

# Validate stage - check code quality and configuration
validate:
  stage: validate
  image: alpine:latest
  script:
    - echo "🔍 Validating project configuration..."
    - |
      if [ ! -f "$DOCKERFILE_PATH" ]; then
        echo "❌ Dockerfile not found at $DOCKERFILE_PATH"
        exit 1
      fi
    - echo "✅ Dockerfile validation passed"
    - |
      if [ ! -d "drupal" ]; then
        echo "❌ Drupal directory not found"
        exit 1
      fi
    - echo "✅ Drupal directory validation passed"
    - echo "✅ All validations passed"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Build PHP image
build:php:
  stage: build
  script:
    - echo "🐘 Building Drupal PHP image..."
    - |
      docker build \
        --target app_php \
        --file $DOCKERFILE_PATH \
        --tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA \
        --tag $PHP_IMAGE_NAME:latest \
        --build-arg USE_XDEBUG=0 \
        .
    - echo "✅ PHP image built successfully"
    - docker images | grep drupal-php
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Build Apache image
build:apache:
  stage: build
  script:
    - echo "🌐 Building Drupal Apache image..."
    - |
      docker build \
        --target apache \
        --file $DOCKERFILE_PATH \
        --tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA \
        --tag $APACHE_IMAGE_NAME:latest \
        --build-arg USE_XDEBUG=0 \
        .
    - echo "✅ Apache image built successfully"
    - docker images | grep drupal-apache
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Push PHP image to Artifact Registry
push:php:
  stage: push
  dependencies:
    - build:php
  before_script:
    - echo "🔐 Authenticating with Google Cloud..."
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev
    - rm /tmp/gcp-key.json
  script:
    - echo "📦 Pushing PHP image to Artifact Registry..."
    - |
      # Rebuild image to ensure it's available
      docker build \
        --target app_php \
        --file $DOCKERFILE_PATH \
        --tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA \
        --build-arg USE_XDEBUG=0 \
        .
    - docker push $PHP_IMAGE_NAME:$CI_COMMIT_SHA
    - |
      if [ "$CI_COMMIT_BRANCH" = "master" ]; then
        docker tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA $PHP_IMAGE_NAME:latest
        docker push $PHP_IMAGE_NAME:latest
        echo "✅ PHP image pushed with 'latest' tag"
      fi
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        docker tag $PHP_IMAGE_NAME:$CI_COMMIT_SHA $PHP_IMAGE_NAME:$CI_COMMIT_TAG
        docker push $PHP_IMAGE_NAME:$CI_COMMIT_TAG
        echo "✅ PHP image pushed with tag '$CI_COMMIT_TAG'"
      fi
    - echo "✅ PHP image pushed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Push Apache image to Artifact Registry
push:apache:
  stage: push
  dependencies:
    - build:apache
  before_script:
    - echo "🔐 Authenticating with Google Cloud..."
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - docker run --rm -v /tmp/gcp-key.json:/tmp/gcp-key.json gcr.io/google.com/cloudsdktool/cloud-sdk:alpine gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev
    - rm /tmp/gcp-key.json
  script:
    - echo "📦 Pushing Apache image to Artifact Registry..."
    - |
      # Rebuild image to ensure it's available
      docker build \
        --target apache \
        --file $DOCKERFILE_PATH \
        --tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA \
        --build-arg USE_XDEBUG=0 \
        .
    - docker push $APACHE_IMAGE_NAME:$CI_COMMIT_SHA
    - |
      if [ "$CI_COMMIT_BRANCH" = "master" ]; then
        docker tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA $APACHE_IMAGE_NAME:latest
        docker push $APACHE_IMAGE_NAME:latest
        echo "✅ Apache image pushed with 'latest' tag"
      fi
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        docker tag $APACHE_IMAGE_NAME:$CI_COMMIT_SHA $APACHE_IMAGE_NAME:$CI_COMMIT_TAG
        docker push $APACHE_IMAGE_NAME:$CI_COMMIT_TAG
        echo "✅ Apache image pushed with tag '$CI_COMMIT_TAG'"
      fi
    - echo "✅ Apache image pushed successfully"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Deploy to development environment
deploy:dev:
  stage: deploy
  image: gcr.io/google.com/cloudsdktool/cloud-sdk:alpine
  dependencies:
    - push:php
    - push:apache
  before_script:
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - gcloud config set project $GCP_PROJECT_ID
  script:
    - echo "🚀 Deploying to development environment..."
    - echo "PHP Image: $PHP_IMAGE_NAME:$CI_COMMIT_SHA"
    - echo "Apache Image: $APACHE_IMAGE_NAME:$CI_COMMIT_SHA"
    - echo "✅ Images are ready for deployment"
    # Tutaj można dodać rzeczywiste kroki wdrożenia, np.:
    # - kubectl set image deployment/drupal-php drupal-php=$PHP_IMAGE_NAME:$CI_COMMIT_SHA
    # - kubectl set image deployment/drupal-apache drupal-apache=$APACHE_IMAGE_NAME:$CI_COMMIT_SHA
  environment:
    name: development
    url: https://dev.contentfactory.example.com
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  when: manual

# Deploy to production environment
deploy:prod:
  stage: deploy
  image: gcr.io/google.com/cloudsdktool/cloud-sdk:alpine
  dependencies:
    - push:php
    - push:apache
  before_script:
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - gcloud config set project $GCP_PROJECT_ID
  script:
    - echo "🚀 Deploying to production environment..."
    - echo "PHP Image: $PHP_IMAGE_NAME:$CI_COMMIT_SHA"
    - echo "Apache Image: $APACHE_IMAGE_NAME:$CI_COMMIT_SHA"
    - echo "✅ Images are ready for deployment"
    # Tutaj można dodać rzeczywiste kroki wdrożenia
  environment:
    name: production
    url: https://contentfactory.example.com
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_TAG
  when: manual

# Cleanup old images (optional job)
cleanup:
  stage: deploy
  image: gcr.io/google.com/cloudsdktool/cloud-sdk:alpine
  before_script:
    - echo $GCP_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/gcp-key.json
    - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - gcloud config set project $GCP_PROJECT_ID
  script:
    - echo "🧹 Cleaning up old Docker images..."
    - |
      # Usuń obrazy starsze niż 30 dni (zachowaj ostatnie 10)
      gcloud artifacts docker images list $DRUPAL_PHP_REGISTRY \
        --sort-by="~createTime" \
        --limit=unlimited \
        --format="value(IMAGE)" | tail -n +11 | head -n -10 | \
        xargs -r gcloud artifacts docker images delete --quiet || true
    - |
      gcloud artifacts docker images list $DRUPAL_APACHE_REGISTRY \
        --sort-by="~createTime" \
        --limit=unlimited \
        --format="value(IMAGE)" | tail -n +11 | head -n -10 | \
        xargs -r gcloud artifacts docker images delete --quiet || true
    - echo "✅ Cleanup completed"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
  allow_failure: true
