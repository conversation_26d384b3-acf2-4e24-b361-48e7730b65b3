# GitLab CI/CD Pipeline - Konfiguracja

## Przegląd

Pipeline GitLab CI/CD dla projektu Content Factory automatyzuje proces budowania obrazów Docker dla aplikacji Drupal. Pipeline składa się z trzech głównych etapów:

1. **Validate** - Walidacja konfiguracji projektu
2. **Build** - Budowanie obrazów Docker (PHP i Apache)
3. **Push** - Wysyłanie obrazów do Google Artifact Registry

## Struktura Pipeline

### Etap 1: Validate
- Sprawdza obecność wymaganych plików (Dockerfile, katalog drupal)
- Uruchamia się dla merge requestów i głównych branchy
- Szybka walidacja przed rozpoczęciem kosztownych operacji

### Etap 2: Build
Dwa równoległe joby:
- **build:php** - Buduje obraz PHP z targetem `app_php`
- **build:apache** - Bud<PERSON>je obraz Apache z targetem `apache`

### Etap 3: Push
Dwa równoległe joby:
- **push:php** - Wysyła obraz PHP do Artifact Registry
- **push:apache** - Wysyła obraz Apache do Artifact Registry

## Wymagane zmienne środowiskowe

Następujące zmienne muszą być skonfigurowane w GitLab CI/CD Settings:

### Wymagane zmienne:
- `GCP_SERVICE_ACCOUNT_KEY` - Klucz service account w formacie base64
  ```bash
  cat gcp-service-account.json | base64 -w 0
  ```
- `TF_VAR_gcp_project` - ID projektu GCP (używane przez Terraform)
  ```bash
  # Przykład: TF_VAR_gcp_project = "clz200508-dp-pl-con-fac-dev"
  ```

### Opcjonalne zmienne (mają wartości domyślne):
- `GCP_REGION` - Region GCP (domyślnie: europe-central2)

## Konfiguracja Service Account

Service Account musi mieć następujące uprawnienia:
- `Artifact Registry Writer` - do wysyłania obrazów
- `Artifact Registry Reader` - do odczytu repozytoriów
- `Storage Object Viewer` - jeśli używane są dodatkowe zasoby

## Repozytoria Artifact Registry

Pipeline używa dwóch repozytoriów utworzonych przez Terraform:
- `drupal-php` - dla obrazów PHP
- `drupal-apache` - dla obrazów Apache

URLs repozytoriów:
- PHP: `europe-central2-docker.pkg.dev/clz200508-dp-pl-con-fac-dev/drupal-php`
- Apache: `europe-central2-docker.pkg.dev/clz200508-dp-pl-con-fac-dev/drupal-apache`

## Tagowanie obrazów

### Strategia tagowania:
- `$CI_COMMIT_SHA` - zawsze dodawany dla każdego commita
- `latest` - dodawany tylko dla brancha `master`
- `$CI_COMMIT_TAG` - dodawany dla tagów Git

### Przykłady:
```
drupal-php:abc123def456  # SHA commita
drupal-php:latest        # Tylko master branch
drupal-php:v1.0.0        # Tag Git
```

## Uruchamianie Pipeline

### Automatyczne uruchamianie:
- Push do brancha `master` - pełny pipeline (validate → build → push)
- Push do brancha `develop` - pełny pipeline (validate → build → push)
- Utworzenie tagu Git - pełny pipeline z tagowaniem obrazów
- Merge Request - tylko walidacja

### Manualne uruchamianie:
- Wszystkie etapy uruchamiają się automatycznie zgodnie z regułami

## Troubleshooting

### Częste problemy:

1. **Błąd autoryzacji GCP**
   - Sprawdź czy `GCP_SERVICE_ACCOUNT_KEY` jest poprawnie zakodowany w base64
   - Sprawdź uprawnienia service account

2. **Błąd budowania obrazu**
   - Sprawdź czy wszystkie pliki są dostępne w kontekście budowania
   - Sprawdź logi Docker build

3. **Błąd push do Artifact Registry**
   - Sprawdź czy repozytoria istnieją w GCP
   - Sprawdź konfigurację Docker authentication

### Przydatne komendy:

```bash
# Sprawdzenie obrazów w repozytorium
gcloud artifacts docker images list europe-central2-docker.pkg.dev/clz200508-dp-pl-con-fac-dev/drupal-php

# Pobranie obrazu lokalnie
docker pull europe-central2-docker.pkg.dev/clz200508-dp-pl-con-fac-dev/drupal-php/drupal-php:latest

# Sprawdzenie logów pipeline w GitLab
# GitLab UI > CI/CD > Pipelines > [wybierz pipeline] > [wybierz job]
```

## Następne kroki

1. Skonfiguruj zmienne środowiskowe w GitLab
2. Przetestuj pipeline na branchu develop
3. Sprawdź czy obrazy są poprawnie wysyłane do Artifact Registry
4. Rozważ dodanie testów automatycznych przed budowaniem
5. Skonfiguruj monitoring repozytoriów w GCP
